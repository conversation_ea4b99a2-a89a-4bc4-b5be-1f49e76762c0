<?php

namespace App\Modules\Domain\Services\UpdateServices;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Util\Constant\QueueConnection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Exception;

class DomainProcessCancellationService
{
    use UserLoggerTrait;

    private $queueConnections = [
        QueueConnection::DOMAIN_REGISTRATION,
        QueueConnection::DOMAIN_RENEWAL,
        QueueConnection::DOMAIN_UPDATE,
        QueueConnection::DOMAIN_CONTACTS_UPDATE,
        QueueConnection::DOMAIN_TRANSFER,
        QueueConnection::DOMAIN_REDEMPTION,
        QueueConnection::DOMAIN_AUTHCODE_REQUEST,
        QueueConnection::DOMAIN_AUTHCODE_UPDATE,
        QueueConnection::DOMAIN_TRANSFER_RESPONSE,
        QueueConnection::DOMAIN_TRANSFER_CANCEL,
        QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE,
        QueueConnection::DOMAIN_SCHEDULE_EXPIRY,
        QueueConnection::DOMAIN_REFRESH,
        QueueConnection::USER_DOMAIN_EXPORT_JOBS,
    ];

    public static function instance(): self
    {
        return new self;
    }

    public function cancelAllProcesses(Collection $domains): void
    {
        foreach ($domains as $domain) {
            try {
                $this->cancelDomainProcesses($domain);
            } catch (Exception $e) {
                app(AuthLogger::class)->error($this->fromWho(
                    'Failed to cancel processes for domain: ' . $domain->name . ' - ' . $e->getMessage(),
                    $domain->user_email
                ));
            }
        }
    }

    private function cancelDomainProcesses(object $domain): void
    {
        app(AuthLogger::class)->info($this->fromWho(
            'Cancelling all processes for expired domain: ' . $domain->name,
            $domain->user_email
        ));

        $cancelledJobsCount = 0;

        foreach ($this->queueConnections as $connection) {
            $cancelledJobsCount += $this->cancelJobsInQueue($connection, $domain);
        }

        $this->cancelScheduledNotifications($domain);

        app(AuthLogger::class)->info($this->fromWho(
            "Cancelled {$cancelledJobsCount} jobs and notifications for domain: " . $domain->name,
            $domain->user_email
        ));
    }

    private function cancelJobsInQueue(string $connection, object $domain): int
    {
        try {
            $tableName = $this->getQueueTableName($connection);
            
            $jobs = DB::table($tableName)
                ->where('payload', 'LIKE', '%"' . $domain->name . '"%')
                ->orWhere('payload', 'LIKE', '%"domain_id":' . $domain->id . '%')
                ->orWhere('payload', 'LIKE', '%"registered_domain_id":' . $domain->registered_domain_id . '%')
                ->get();

            if ($jobs->isEmpty()) {
                return 0;
            }

            $jobIds = $jobs->pluck('id')->toArray();
            
            $deletedCount = DB::table($tableName)->whereIn('id', $jobIds)->delete();

            if ($deletedCount > 0) {
                app(AuthLogger::class)->info($this->fromWho(
                    "Cancelled {$deletedCount} jobs in {$connection} queue for domain: " . $domain->name,
                    $domain->user_email
                ));
            }

            return $deletedCount;

        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho(
                "Error cancelling jobs in {$connection} for domain {$domain->name}: " . $e->getMessage(),
                $domain->user_email
            ));
            return 0;
        }
    }

    private function cancelScheduledNotifications(object $domain): void
    {
        try {
            $deletedCount = DB::table(table: 'domain_expiry_notifications')
                ->where('domain_id', $domain->id)
                ->where('status', 'scheduled')
                ->delete();

            if ($deletedCount > 0) {
                app(AuthLogger::class)->info($this->fromWho(
                    "Cancelled {$deletedCount} scheduled notifications for domain: " . $domain->name,
                    $domain->user_email
                ));
            }

        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho(
                "Error cancelling notifications for domain {$domain->name}: " . $e->getMessage(),
                $domain->user_email
            ));
        }
    }

    private function getQueueTableName(string $connection): string
    {
        $queueTableMap = [
            QueueConnection::DOMAIN_REGISTRATION => 'domain_registration_jobs',
            QueueConnection::DOMAIN_RENEWAL => 'domain_renewal_jobs',
            QueueConnection::DOMAIN_UPDATE => 'domain_update_jobs',
            QueueConnection::DOMAIN_CONTACTS_UPDATE => 'domain_contacts_update_jobs',
            QueueConnection::DOMAIN_TRANSFER => 'domain_transfer_jobs',
            QueueConnection::DOMAIN_REDEMPTION => 'domain_redemption_jobs',
            QueueConnection::DOMAIN_AUTHCODE_REQUEST => 'domain_authcode_request_jobs',
            QueueConnection::DOMAIN_AUTHCODE_UPDATE => 'domain_authcode_update_jobs',
            QueueConnection::DOMAIN_TRANSFER_RESPONSE => 'send_transfer_request_response_jobs',
            QueueConnection::DOMAIN_TRANSFER_CANCEL => 'cancel_domain_transfer_jobs',
            QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE => 'poll_update_domain_transfer_jobs',
            QueueConnection::DOMAIN_SCHEDULE_EXPIRY => 'domain_exp_notif_sched_jobs',
            QueueConnection::DOMAIN_REFRESH => 'domain_refresh_jobs',
            QueueConnection::USER_DOMAIN_EXPORT_JOBS => 'user_domain_export_jobs',
        ];

        return $queueTableMap[$connection];
    }
}
