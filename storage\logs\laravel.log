[2025-03-05 02:25:36] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:25:40] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:25:48] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:26:37] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:29:41] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:08] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:12] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:28] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:32] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:34] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:37] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:30:40] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:33:00] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:33:57] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:34:01] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:34:19] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:35:05] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:35:11] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:41:13] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}}} 
[2025-03-05 02:41:33] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"test 5","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:42:53] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:15] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:17] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:33] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:36] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:40] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:42] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:44] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:44:50] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:45:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:45:37] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:49:56] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:49:58] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:50:05] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:13] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:17] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:21] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:36] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:51:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:52:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:16] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:26] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:34] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:47] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 02:54:49] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:20] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:30] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:41] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:04:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:05:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:05:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:06:00] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:06:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:06:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:06:44] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:06:55] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:17] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:18] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:22] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:07:28] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:12:59] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:09] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:16] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:30] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:54] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:13:56] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:02] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:08] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:11] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:21] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:36] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:41] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:14:47] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:33] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:42] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:48] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:49] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:15:56] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:16:05] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:16:11] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:16:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:16:59] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:17:05] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:18:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:18:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:22:59] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:03] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:07] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:37] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:46] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:23:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:24:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:00] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:09] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:10] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:16] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:18] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:22] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:25:26] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:26:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:26:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:26:39] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:26:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:26:46] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:28:34] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:48:38] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:49:09] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:49:49] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:51:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 03:51:32] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:00:53] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:00:57] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:02:55] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:02:57] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:03:05] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:03:47] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:03:50] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:03:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:03:55] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:05:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:06:12] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:06:16] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:06:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:06:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:24:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:25:13] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:26:45] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:27:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:32:47] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:33:08] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:34:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:37:57] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:43:12] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:44:38] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:44:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:45:03] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:49:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:49:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:51:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:52:20] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:53:22] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:53:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:53:55] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:53:58] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:54:15] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:54:21] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:54:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:55:58] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:00] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:02] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:03] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:04] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:08] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:10] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:12] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 05:56:15] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:03:11] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:04:15] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:05:37] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:05:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:06:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:07:35] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:09:46] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:10:03] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:11:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:11:17] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:12:00] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:14:36] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:17:56] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:18:21] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:18:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:18:27] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:14] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:21] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:30] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:32] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:42] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:43] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:19:44] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:19:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:19:56] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:19:58] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:20:01] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:20:03] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:20:06] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:20:12] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":10,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"one-time","time":"17:02:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":9,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-04","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":8,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"17:02:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,1","created_at":null,"updated_at":null},{"id":7,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"monthly","time":"15:22:00","start_date":"2025-03-10","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":6,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":5,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"weekly","time":"15:20:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2","created_at":null,"updated_at":null},{"id":4,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Normal","status":"pending","schedule_type":"one-time","time":"15:20:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null},{"id":3,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-07","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":2,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-05","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null},{"id":1,"user_id":8,"title":"test","message":"test","link_name":"test","redirect_url":"test","type":"Important","status":"pending","schedule_type":"weekly","time":"15:10:00","start_date":"2025-03-03","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":"0,2,4","created_at":null,"updated_at":null}]}} 
[2025-03-05 06:21:18] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:21:31] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:34:22] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:38:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:39:32] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:40:07] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:41:39] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:43:07] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:43:10] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:43:42] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:43:48] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:43:50] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:46:24] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:47:06] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:47:07] local.ERROR: Error fetching all user notifications: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "link_url" does not exist
LINE 1: select "id", "title", "message", "link_url", "link_name", "c...
                                         ^ (Connection: pgsql, SQL: select "id", "title", "message", "link_url", "link_name", "created_at" from "schedule_notifications" where "user_id" = 8 order by "id" desc)  
[2025-03-05 06:47:31] local.ERROR: Error fetching all user notifications: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "link_url" does not exist
LINE 1: select "id", "title", "message", "link_url", "link_name", "c...
                                         ^ (Connection: pgsql, SQL: select "id", "title", "message", "link_url", "link_name", "created_at" from "schedule_notifications" where "user_id" = 8 order by "id" desc)  
[2025-03-05 06:47:32] local.ERROR: Error fetching all user notifications: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "link_url" does not exist
LINE 1: select "id", "title", "message", "link_url", "link_name", "c...
                                         ^ (Connection: pgsql, SQL: select "id", "title", "message", "link_url", "link_name", "created_at" from "schedule_notifications" where "user_id" = 8 order by "id" desc)  
[2025-03-05 06:47:40] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:48:13] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:48:15] local.ERROR: Error fetching all user notifications: SQLSTATE[42703]: Undefined column: 7 ERROR:  column "link_url" does not exist
LINE 1: select "id", "title", "message", "link_url", "link_name", "c...
                                         ^ (Connection: pgsql, SQL: select "id", "title", "message", "link_url", "link_name", "created_at" from "schedule_notifications" where "user_id" = 8 order by "id" desc)  
[2025-03-05 06:49:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:49:27] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:26] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:50:30] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:33] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:36] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:42] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:44] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:50:46] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:51:40] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:51:42] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:51:45] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:51:48] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:53:23] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:53:24] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:53:27] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:53:30] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:53:32] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:55:57] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:56:52] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:56:54] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:56:56] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:56:58] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:57:00] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:57:02] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:57:04] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 06:57:18] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:57:23] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 06:57:25] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:00:40] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:00:48] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:00:52] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:00:54] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:02:14] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:02:17] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:02:20] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:02:43] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:02:45] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:02:46] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:02:52] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:03:40] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:03:45] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:03:49] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:05:33] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:05:34] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","start_date":"2025-03-14"},{"id":10,"title":"test","message":"test","link_name":"test","start_date":"2025-03-05"},{"id":9,"title":"test","message":"test","link_name":"test","start_date":"2025-03-04"},{"id":8,"title":"test","message":"test","link_name":"test","start_date":"2025-03-03"},{"id":7,"title":"test","message":"test","link_name":"test","start_date":"2025-03-10"},{"id":6,"title":"test","message":"test","link_name":"test","start_date":"2025-03-05"},{"id":5,"title":"test","message":"test","link_name":"test","start_date":"2025-03-03"},{"id":4,"title":"test","message":"test","link_name":"test","start_date":"2025-03-05"},{"id":3,"title":"test","message":"test","link_name":"test","start_date":"2025-03-07"},{"id":2,"title":"test","message":"test","link_name":"test","start_date":"2025-03-05"},{"id":1,"title":"test","message":"test","link_name":"test","start_date":"2025-03-03"}]}} 
[2025-03-05 07:05:54] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:06:03] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:06:04] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:06:42] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:06:44] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:07:37] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:08:10] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:08:29] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:08:30] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[]}} 
[2025-03-05 07:08:58] local.INFO: Fetched Notification: {"notification":{"stdClass":{"id":11,"user_id":8,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","redirect_url":"profile","type":"Important","status":"pending","schedule_type":"monthly","time":"17:02:00","start_date":"2025-03-14","min_registration_period":null,"max_registration_period":null,"expiration":null,"weekday":null,"created_at":null,"updated_at":null}}} 
[2025-03-05 07:08:59] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-05 07:09:14] local.INFO: Fetched All Notifications: {"notifications":{"Illuminate\\Support\\Collection":[{"id":11,"title":"Update Profile Confirmation","message":"We kindly request you to update your contact information to ensure you receive important notifications and updates. Please check and update your phone number, email, and address as needed","link_name":"read more...","created_at":null},{"id":10,"title":"test","message":"test","link_name":"test","created_at":null},{"id":9,"title":"test","message":"test","link_name":"test","created_at":null},{"id":8,"title":"test","message":"test","link_name":"test","created_at":null},{"id":7,"title":"test","message":"test","link_name":"test","created_at":null},{"id":6,"title":"test","message":"test","link_name":"test","created_at":null},{"id":5,"title":"test","message":"test","link_name":"test","created_at":null},{"id":4,"title":"test","message":"test","link_name":"test","created_at":null},{"id":3,"title":"test","message":"test","link_name":"test","created_at":null},{"id":2,"title":"test","message":"test","link_name":"test","created_at":null},{"id":1,"title":"test","message":"test","link_name":"test","created_at":null}]}} 
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 1  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 2  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 3  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 4  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 5  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 6  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 7  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 8  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 9  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 10  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 11  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 12  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 13  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 14  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 15  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 16  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 17  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 18  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 19  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 20  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 21  
[2025-03-06 03:31:02] local.INFO: General notification dispatched: 22  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 1  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 2  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 3  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 4  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 5  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 6  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 7  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 8  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 9  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 10  
[2025-03-06 05:17:03] local.INFO: General notification dispatched: 11  
[2025-03-06 05:34:07] local.INFO: Fetched Notifications:   
[2025-03-06 05:34:22] local.INFO: Fetched Notifications:   
[2025-03-06 05:34:33] local.INFO: Fetched Notifications:   
[2025-03-06 05:34:41] local.INFO: Fetched Notifications:   
[2025-03-06 05:34:46] local.INFO: Fetched Notifications:   
[2025-03-06 05:34:50] local.INFO: Fetched Notifications:   
[2025-03-06 05:37:22] local.INFO: Fetching notifications for user ID: 8  
[2025-03-06 05:37:22] local.INFO: Fetched Notifications:   
[2025-03-06 05:37:22] local.INFO: Fetched Notifications:   
[2025-03-06 05:37:37] local.INFO: Fetching notifications for user ID: 8  
[2025-03-06 05:37:37] local.INFO: Fetched Notifications:   
[2025-03-06 05:37:37] local.INFO: Fetched Notifications:   
[2025-03-06 05:38:21] local.INFO: Fetching notifications for user ID: 8  
[2025-03-06 05:38:21] local.INFO: Current Date: 2025-03-06  
[2025-03-06 05:38:21] local.INFO: Current Time: 05:38:21  
[2025-03-06 05:38:21] local.INFO: Fetched Notifications:   
[2025-03-06 05:38:21] local.INFO: Fetched Notifications:   
[2025-03-06 05:45:35] local.INFO: Fetched Notifications:  [{"stdClass":{"id":10,"user_id":8,"title":"This is a test","message":"testing testing 123123","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"13:15:00","start_date":"2025-03-06","min_registration_period":null,"max_registration_period":null,"expiration":null,"created_at":"2025-03-06 05:14:10","updated_at":"2025-03-06 05:14:10"}}] 
[2025-03-06 05:45:55] local.INFO: Fetched Notifications:  [{"stdClass":{"id":10,"user_id":8,"title":"This is a test","message":"testing testing 123123","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"13:15:00","start_date":"2025-03-06","min_registration_period":null,"max_registration_period":null,"expiration":null,"created_at":"2025-03-06 05:14:10","updated_at":"2025-03-06 05:14:10"}}] 
[2025-03-06 05:46:47] local.INFO: Fetched Notifications:  [{"stdClass":{"id":10,"user_id":8,"title":"This is a test","message":"testing testing 123123","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"13:15:00","start_date":"2025-03-06","min_registration_period":null,"max_registration_period":null,"expiration":null,"created_at":"2025-03-06 05:14:10","updated_at":"2025-03-06 05:14:10"}}] 
[2025-03-06 05:46:52] local.INFO: Fetched Notifications:  [{"stdClass":{"id":10,"user_id":8,"title":"This is a test","message":"testing testing 123123","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"13:15:00","start_date":"2025-03-06","min_registration_period":null,"max_registration_period":null,"expiration":null,"created_at":"2025-03-06 05:14:10","updated_at":"2025-03-06 05:14:10"}}] 
[2025-03-06 05:46:54] local.INFO: Fetched Notifications:  [{"stdClass":{"id":10,"user_id":8,"title":"This is a test","message":"testing testing 123123","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"13:15:00","start_date":"2025-03-06","min_registration_period":null,"max_registration_period":null,"expiration":null,"created_at":"2025-03-06 05:14:10","updated_at":"2025-03-06 05:14:10"}}] 
[2025-03-06 05:47:39] local.INFO: Fetched Notifications:   
[2025-03-06 05:47:52] local.INFO: Fetched Notifications:   
[2025-03-06 05:47:56] local.INFO: Fetched Notifications:   
[2025-03-06 05:51:07] local.INFO: Fetched Notifications:   
[2025-03-06 05:51:11] local.INFO: Fetched Notifications:   
[2025-03-06 05:51:14] local.INFO: Fetched Notifications:   
[2025-03-06 05:51:16] local.INFO: Fetched Notifications:   
[2025-03-06 05:53:14] local.INFO: Fetched Notifications:   

asd



[2025-03-06 06:01:02] local.INFO: Current Date: 2025-03-06  
[2025-03-06 06:01:02] local.INFO: Current Time: 06:01:02  
[2025-03-06 06:01:02] local.INFO: Checking notification ID: 12, Start Date: 2025-03-06, Time: 14:56:00  
[2025-03-06 06:02:09] local.INFO: Current Date: 2025-03-06  
[2025-03-06 06:02:09] local.INFO: Current Time: 06:02:09  
[2025-03-06 06:02:09] local.INFO: Checking notification ID: 12, Start Date: 2025-03-06, Time: 14:56:00  
[2025-03-06 06:02:09] local.INFO: General notification dispatched: 12  
[2025-03-06 06:03:05] local.INFO: Current Date: 2025-03-06  
[2025-03-06 06:03:05] local.INFO: Current Time: 06:03:05  
[2025-03-06 06:03:08] local.INFO: Fetched Notifications:  [{"stdClass":{"id":12,"user_id":8,"title":"Update Contact Information","message":"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"14:56:00","start_date":"2025-03-06","min_registration_period":2,"max_registration_period":365,"expiration":null,"created_at":"2025-03-06 05:49:51","updated_at":"2025-03-06 05:49:51"}}] 
[2025-03-06 06:04:05] local.INFO: Current Date: 2025-03-06  
[2025-03-06 06:04:05] local.INFO: Current Time: 06:04:05  
[2025-03-06 06:19:05] local.INFO: Fetched Notifications:  [{"stdClass":{"id":12,"user_id":8,"title":"Update Contact Information","message":"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book","link_name":"read more...","redirect_url":"/profile","type":"Important","status":"active","schedule_type":"one-time","time":"14:56:00","start_date":"2025-03-06","min_registration_period":2,"max_registration_period":365,"expiration":null,"created_at":"2025-03-06 05:49:51","updated_at":"2025-03-06 05:49:51"}}] 
[2025-03-07 02:20:05] local.INFO: Domain history event created: PUSH_REQUEST_APPROVAL  
[2025-03-07 10:10:28] local.INFO: Identity Status:   


[2025-03-10 05:09:24] local.INFO: Getting user notifications: {"user_id":8} 
[2025-03-10 05:09:24] local.INFO: Fetching notifications for user: {"user_id":8} 
[2025-03-10 05:09:24] local.INFO: Found notifications: {"count":0,"notifications":{"Illuminate\\Support\\Collection":[]}} 
[2025-03-10 05:09:24] local.INFO: Returning notifications: {"count":0} 
[2025-03-10 05:09:27] local.INFO: Getting user notifications: {"user_id":8} 
[2025-03-10 05:09:27] local.INFO: Fetching notifications for user: {"user_id":8} 
[2025-03-10 05:09:27] local.INFO: Found notifications: {"count":0,"notifications":{"Illuminate\\Support\\Collection":[]}} 
[2025-03-10 05:09:27] local.INFO: Returning notifications: {"count":0} 
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 27: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 26: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 25: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 24: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 23: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 22: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 21: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 20: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 19: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 18: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 17: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 16: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 14: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 13: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 12: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 11: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 9: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 7: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 6: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 5: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 4: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:14:04] local.ERROR: Error processing expiration for notification 3: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 27: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 26: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 25: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 24: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 23: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 22: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 21: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 20: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 19: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 18: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 17: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 16: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 14: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 13: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 12: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 11: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 9: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 7: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 6: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 5: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 4: Undefined property: stdClass::$expiration_time  
[2025-03-11 01:15:04] local.ERROR: Error processing expiration for notification 3: Undefined property: stdClass::$expiration_time  
[2025-03-11 08:09:20] local.INFO: Received request data: {"id":22} 
[2025-03-11 08:09:20] local.INFO: Processing notification: {"id":22} 
[2025-03-11 08:09:20] local.INFO: Update result: {"updated":1} 
[2025-03-11 08:12:24] local.INFO: Received mark as read request: {"request_data":{"id":15},"user_id":8} 
[2025-03-11 08:12:26] local.INFO: Received mark as read request: {"request_data":{"id":15},"user_id":8} 
[2025-03-11 08:13:20] local.INFO: Received mark as read request: {"request_data":{"id":18},"user_id":8} 
[2025-03-11 08:13:22] local.INFO: Received mark as read request: {"request_data":{"id":18},"user_id":8} 
[2025-03-11 08:13:33] local.INFO: Received mark as read request: {"request_data":{"id":2},"user_id":8} 
[2025-03-11 08:13:34] local.INFO: Received mark as read request: {"request_data":{"id":2},"user_id":8} 


[2025-03-20 06:21:13] local.INFO: Announcement Query: {"userId":8,"sql":"select \"id\", \"user_id\", \"title\", \"message\", \"link_name\", \"created_at\", \"time\", \"start_date\", \"schedule_type\", \"status\", \"type\", \"expiration\", \"updated_at\", \"min_registration_period\", \"max_registration_period\", \"redirect_url\", \"read_at\" from \"public\".\"schedule_notifications\" where \"user_id\" = ? and \"status\" = ? and \"type\" = ? order by \"updated_at\" desc","bindings":[8,"active","announcement"]} 
[2025-03-20 06:21:13] local.INFO: Announcement Results: {"total":0,"items":{"Illuminate\\Support\\Collection":[]}} 
[2025-03-20 06:23:11] local.INFO: Announcement Query: {"userId":8,"sql":"select \"id\", \"user_id\", \"title\", \"message\", \"link_name\", \"created_at\", \"time\", \"start_date\", \"schedule_type\", \"status\", \"type\", \"expiration\", \"updated_at\", \"min_registration_period\", \"max_registration_period\", \"redirect_url\", \"read_at\" from \"public\".\"schedule_notifications\" where \"user_id\" = ? and \"status\" = ? order by \"updated_at\" desc","bindings":[8,"active"]} 
[2025-03-20 06:23:11] local.INFO: Announcement Results: {"total":34,"items":{"Illuminate\\Support\\Collection":[{"id":602,"user_id":8,"title":"test123","message":"test","link_name":"test","created_at":"2025-03-20 02:40:53","time":"11:40:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:40:53","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":"2025-03-20 03:03:28"},{"id":593,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-20 02:40:16","time":"11:40:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:40:16","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":582,"user_id":8,"title":"test me","message":"test","link_name":"for test","created_at":"2025-03-20 02:39:23","time":"11:39:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:39:23","min_registration_period":null,"max_registration_period":null,"redirect_url":"/notification","read_at":null},{"id":571,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-20 02:38:26","time":"11:38:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:38:26","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":560,"user_id":8,"title":"test","message":"me","link_name":"test","created_at":"2025-03-20 02:37:10","time":"11:36:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:37:10","min_registration_period":null,"max_registration_period":null,"redirect_url":"/profile","read_at":null},{"id":538,"user_id":8,"title":"Update Contact Information","message":"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.","link_name":"test","created_at":"2025-03-20 02:00:05","time":"11:59:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 02:00:36","min_registration_period":1,"max_registration_period":365,"redirect_url":"/profile","read_at":null},{"id":416,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-19 05:45:37","time":"14:45:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:00:36","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":405,"user_id":8,"title":"test656","message":"tes","link_name":"test","created_at":"2025-03-19 05:44:53","time":"15:44:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:00:36","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":"2025-03-20 02:03:01"},{"id":372,"user_id":8,"title":"tet","message":"tet","link_name":"tes","created_at":"2025-03-19 05:40:15","time":"15:40:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:00:36","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":427,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-19 05:47:00","time":"15:46:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 02:00:36","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null}]}} 
[2025-03-20 06:23:15] local.INFO: Announcement Query: {"userId":8,"sql":"select \"id\", \"user_id\", \"title\", \"message\", \"link_name\", \"created_at\", \"time\", \"start_date\", \"schedule_type\", \"status\", \"type\", \"expiration\", \"updated_at\", \"min_registration_period\", \"max_registration_period\", \"redirect_url\", \"read_at\" from \"public\".\"schedule_notifications\" where \"user_id\" = ? and \"status\" = ? order by \"updated_at\" desc","bindings":[8,"active"]} 
[2025-03-20 06:23:15] local.INFO: Announcement Results: {"total":34,"items":{"Illuminate\\Support\\Collection":[{"id":230,"user_id":8,"title":"tes","message":"test","link_name":"test","created_at":"2025-03-19 03:30:11","time":"00:30:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":449,"user_id":8,"title":"etst","message":"sts","link_name":"test","created_at":"2025-03-19 06:06:06","time":"15:05:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":361,"user_id":8,"title":"testgg","message":"test","link_name":"test","created_at":"2025-03-19 05:38:46","time":"15:38:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":473,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":474,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":475,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":465,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":466,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":467,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":468,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null}]}} 
[2025-03-20 06:23:16] local.INFO: Announcement Query: {"userId":8,"sql":"select \"id\", \"user_id\", \"title\", \"message\", \"link_name\", \"created_at\", \"time\", \"start_date\", \"schedule_type\", \"status\", \"type\", \"expiration\", \"updated_at\", \"min_registration_period\", \"max_registration_period\", \"redirect_url\", \"read_at\" from \"public\".\"schedule_notifications\" where \"user_id\" = ? and \"status\" = ? order by \"updated_at\" desc","bindings":[8,"active"]} 
[2025-03-20 06:23:16] local.INFO: Announcement Results: {"total":34,"items":{"Illuminate\\Support\\Collection":[{"id":469,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":470,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":471,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":472,"user_id":8,"title":"test","message":"asd","link_name":"test","created_at":"2025-03-19 07:41:12","time":"16:41:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Normal","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":"2025-03-20 01:56:53"},{"id":7,"user_id":8,"title":"This is One time","message":"aksodijas dijasid baisbd ihabshidb ahsbdjh asdas","link_name":"click me...","created_at":"2025-03-12 07:29:02","time":"15:28:00","start_date":"2025-03-12","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":1,"max_registration_period":7,"redirect_url":"/profile","read_at":null},{"id":58,"user_id":8,"title":"test asd asd asd","message":"tesasd asd","link_name":"one","created_at":"2025-03-12 07:50:04","time":"16:49:00","start_date":"2025-03-13","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":1,"max_registration_period":7,"redirect_url":"/notification","read_at":null},{"id":57,"user_id":8,"title":"test one one one","message":"etsdfa dsfasdfa sdfa","link_name":"link","created_at":"2025-03-12 07:49:19","time":"15:49:00","start_date":"2025-03-12","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":1,"max_registration_period":7,"redirect_url":"/profile","read_at":null},{"id":56,"user_id":8,"title":"test one time","message":"asdasda s","link_name":"test","created_at":"2025-03-12 07:48:02","time":"15:47:00","start_date":"2025-03-12","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":1,"max_registration_period":7,"redirect_url":"/notification","read_at":null},{"id":331,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-19 05:30:36","time":"14:30:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":"2025-03-20 01:57:30"},{"id":18,"user_id":8,"title":"This is One time","message":"aksodijas dijasid baisbd ihabshidb ahsbdjh asdas","link_name":"click me...","created_at":"2025-03-12 07:30:06","time":"15:28:00","start_date":"2025-03-12","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/profile","read_at":null}]}} 
[2025-03-20 06:23:16] local.INFO: Announcement Query: {"userId":8,"sql":"select \"id\", \"user_id\", \"title\", \"message\", \"link_name\", \"created_at\", \"time\", \"start_date\", \"schedule_type\", \"status\", \"type\", \"expiration\", \"updated_at\", \"min_registration_period\", \"max_registration_period\", \"redirect_url\", \"read_at\" from \"public\".\"schedule_notifications\" where \"user_id\" = ? and \"status\" = ? order by \"updated_at\" desc","bindings":[8,"active"]} 
[2025-03-20 06:23:16] local.INFO: Announcement Results: {"total":34,"items":{"Illuminate\\Support\\Collection":[{"id":272,"user_id":8,"title":"test","message":"test","link_name":"test","created_at":"2025-03-19 05:15:36","time":"14:15:00","start_date":"2025-03-19","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":284,"user_id":8,"title":"test1","message":"test","link_name":"test","created_at":"2025-03-19 05:23:25","time":"17:23:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":210,"user_id":8,"title":"asd","message":"234","link_name":"test","created_at":"2025-03-19 03:26:03","time":"00:25:00","start_date":"2025-03-20","schedule_type":"one-time","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:50:04","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":null},{"id":320,"user_id":8,"title":"test5","message":"test","link_name":"test","created_at":"2025-03-19 05:27:20","time":"15:27:00","start_date":"2025-03-27","schedule_type":"weekly","status":"active","type":"Important","expiration":null,"updated_at":"2025-03-20 01:07:02","min_registration_period":null,"max_registration_period":null,"redirect_url":"/test","read_at":"2025-03-20 01:24:40"}]}} 
[2025-03-27 08:56:01] local.INFO: Identity Status:   
[2025-03-28 01:56:05] local.INFO: Identity Status:   
[2025-04-02 01:44:24] local.ERROR: Error creating email history: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "user_id" of relation "email_histories" violates not-null constraint
DETAIL:  Failing row contains (1, null, <EMAIL>, <EMAIL>, Email Verification Code, OTP Verification, {"email":"<EMAIL>","token":"nVP4WmRErqNSuVfSc892SOUa..., null, 2025-04-02 01:44:24, 2025-04-02 01:44:24). (Connection: pgsql, SQL: insert into "public"."email_histories" ("user_id", "name", "recipient_email", "subject", "email_body", "email_type", "attachment", "updated_at", "created_at") values (?, <EMAIL>, <EMAIL>, Email Verification Code, {"email":"<EMAIL>","token":"nVP4WmRErqNSuVfSc892SOUaPY4gJeoF","code":397392}, OTP Verification, ?, 2025-04-02 01:44:24, 2025-04-02 01:44:24) returning "id")  
[2025-04-02 02:09:33] local.ERROR: Error creating email history: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "user_id" of relation "email_histories" violates not-null constraint
DETAIL:  Failing row contains (2, null, Mike Julius, <EMAIL>, Email Otp Mail, OTP Verification, {"code":848090,"username":"Mike Julius","userAgent":"Chrome","co..., null, 2025-04-02 02:09:33, 2025-04-02 02:09:33). (Connection: pgsql, SQL: insert into "public"."email_histories" ("user_id", "name", "recipient_email", "subject", "email_body", "email_type", "attachment", "updated_at", "created_at") values (?, Mike Julius, <EMAIL>, Email Otp Mail, {"code":848090,"username":"Mike Julius","userAgent":"Chrome","codeValidUntil":"2025-04-02T02:14:33.273011Z","email":"<EMAIL>"}, OTP Verification, ?, 2025-04-02 02:09:33, 2025-04-02 02:09:33) returning "id")  
[2025-04-02 02:12:45] local.ERROR: Error creating email history: SQLSTATE[23502]: Not null violation: 7 ERROR:  null value in column "user_id" of relation "email_histories" violates not-null constraint
DETAIL:  Failing row contains (3, null, Mike Julius, <EMAIL>, Email Otp Mail, OTP Verification, {"code":257886,"username":"Mike Julius","userAgent":"Chrome","co..., null, 2025-04-02 02:12:45, 2025-04-02 02:12:45). (Connection: pgsql, SQL: insert into "public"."email_histories" ("user_id", "name", "recipient_email", "subject", "email_body", "email_type", "attachment", "updated_at", "created_at") values (?, Mike Julius, <EMAIL>, Email Otp Mail, {"code":257886,"username":"Mike Julius","userAgent":"Chrome","codeValidUntil":"2025-04-02T02:17:45.264248Z","email":"<EMAIL>"}, OTP Verification, ?, 2025-04-02 02:12:45, 2025-04-02 02:12:45) returning "id")  

[2025-04-02 02:14:09] local.INFO: Passed: OTP Verification  
[2025-04-02 02:24:30] local.INFO: Passed: OTP Verification  
[2025-04-02 02:26:46] local.INFO: Passed: OTP Verification  
[2025-05-14 03:04:19] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export initiated - File: domains_2025-05-14-030419.csv - Records: All, {\"filterColumns\":[\"registrant\",\"status\",\"category\",\"techContact\",\"adminContact\",\"billingContact\",\"nameservers\",\"expiryDate\",\"createdDate\",\"updatedDate\"],\"filterStatus\":null,\"filterOrderBy\":null,\"filterName\":null,\"filterTld\":null,\"filterCategory\":null,\"filterNameserver\":null}, 2025-05-14 03:04:19, 2025-05-14 03:04:19) returning \"id\")"} 
[2025-05-14 03:08:08] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generation initiated, {\"filterColumns\":[\"registrant\",\"status\",\"category\",\"techContact\",\"adminContact\",\"billingContact\",\"nameservers\",\"expiryDate\",\"createdDate\",\"updatedDate\"],\"filterStatus\":null,\"filterOrderBy\":null,\"filterName\":null,\"filterTld\":null,\"filterCategory\":null,\"filterNameserver\":null}, 2025-05-14 03:08:08, 2025-05-14 03:08:08) returning \"id\")"} 
[2025-05-14 03:11:37] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generation initiated, {\"filterColumns\":[\"registrant\",\"status\",\"category\",\"techContact\",\"adminContact\",\"billingContact\",\"nameservers\",\"expiryDate\",\"createdDate\",\"updatedDate\"],\"filterStatus\":null,\"filterOrderBy\":null,\"filterName\":null,\"filterTld\":null,\"filterCategory\":null,\"filterNameserver\":null}, 2025-05-14 03:11:37, 2025-05-14 03:11:37) returning \"id\")"} 
[2025-05-14 03:14:40] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generated successfully - domains_2025-05-14-031428.csv, {\"filterColumns\":[\"registrant\",\"status\",\"category\",\"techContact\",\"adminContact\",\"billingContact\",\"nameservers\",\"expiryDate\",\"createdDate\",\"updatedDate\"],\"filterStatus\":null,\"filterOrderBy\":null,\"filterName\":null,\"filterTld\":null,\"filterCategory\":null,\"filterNameserver\":null}, 2025-05-14 03:14:40, 2025-05-14 03:14:40) returning \"id\")"} 
[2025-05-14 03:18:27] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generated: domains_2025-05-14-031816.csv, \"Name\",\"Registrant\",\"Status\",\"Category\",\"Admin Contact\",\"Tech Contact\",\"Billing Contact\",\"Nameservers\",\"Expiry Date\",\"Created Date\",\"Updated Date\"
\"majakaw.net\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:46\"
\"majakaw.com\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:50\"
\"majakaw.org\",\"mj21747190557\",\"ACTIVE\",\"Default\",\"mj21747190557\",\"mj21747190557\",\"mj21747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:52\"
, 2025-05-14 03:18:27, 2025-05-14 03:18:27) returning \"id\")"} 
[2025-05-14 03:19:13] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generated: domains_2025-05-14-031900.csv, \"Name\",\"Registrant\",\"Status\",\"Category\",\"Admin Contact\",\"Tech Contact\",\"Billing Contact\",\"Nameservers\",\"Expiry Date\",\"Created Date\",\"Updated Date\"
\"majakaw.net\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:46\"
\"majakaw.com\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:50\"
\"majakaw.org\",\"mj21747190557\",\"ACTIVE\",\"Default\",\"mj21747190557\",\"mj21747190557\",\"mj21747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:52\"
, 2025-05-14 03:19:13, 2025-05-14 03:19:13) returning \"id\")"} 
[2025-05-14 03:19:13] local.ERROR: Error processing client activity {"user_id":1,"error_message":"SQLSTATE[22001]: String data, right truncated: 7 ERROR:  value too long for type character varying(255) (Connection: pgsql, SQL: insert into \"user_transaction_histories\" (\"user_id\", \"type\", \"message\", \"link\", \"updated_at\", \"created_at\") values (1, DOMAIN_UPDATE, Domain export file generated: domains_2025-05-14-031901.csv, \"Name\",\"Registrant\",\"Status\",\"Category\",\"Admin Contact\",\"Tech Contact\",\"Billing Contact\",\"Nameservers\",\"Expiry Date\",\"Created Date\",\"Updated Date\"
\"majakaw.net\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:46\"
\"majakaw.com\",\"mj11747190557\",\"ACTIVE\",\"Default\",\"mj11747190557\",\"mj11747190557\",\"mj11747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:50\"
\"majakaw.org\",\"mj21747190557\",\"ACTIVE\",\"Default\",\"mj21747190557\",\"mj21747190557\",\"mj21747190557\",\"\",\"05/14/2026\",\"2025-05-14 03:03:25\",\"2025-05-14 03:03:52\"
, 2025-05-14 03:19:13, 2025-05-14 03:19:13) returning \"id\")"} 


[2025-05-21 07:03:47] local.INFO: Domain Privacy Update Request {"domain_id":53,"domain_user_id":null,"auth_user_id":2,"privacy_value":false} 
[2025-05-21 07:03:47] local.ERROR: Domain Privacy Update Permission Error {"domain_id":53,"domain_user_id":null,"auth_user_id":2} 

[2025-05-22 08:48:42] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:48:42] local.INFO: WhoIsSearchRequest@rules called  
[2025-05-22 08:48:58] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:48:58] local.INFO: WhoIsSearchRequest@rules called  
[2025-05-22 08:48:58] local.INFO: WhoIsController@search called {"domain":"restaurantzxc.org","all_data":{"domain":"restaurantzxc.org"}} 
[2025-05-22 08:48:58] local.INFO: WhoIsSearchRequest@search called {"input":{"domain":"restaurantzxc.org"}} 
[2025-05-22 08:53:40] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:54:05] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:54:38] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:55:14] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:55:19] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:55:22] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:55:24] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 08:55:26] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-22 09:51:42] local.INFO: WhoIsSearchRequest@authorize called  
[2025-05-27 01:33:07] local.ERROR: Attempted to send an email without an authenticated user.  
[2025-05-29 03:29:36] local.INFO: Identity Status:   
[2025-05-29 03:30:03] local.INFO: Identity Status:   
[2025-05-29 08:22:01] local.INFO: Identity Status:   
[2025-06-02 05:40:17] local.INFO: Identity Status:   
[2025-06-02 05:40:45] local.INFO: Identity Status:   
[2025-06-02 05:47:38] local.INFO: Identity Status:   
[2025-06-02 05:47:52] local.INFO: Identity Status:   
[2025-06-02 05:47:56] local.INFO: Identity Status:   
[2025-06-02 05:47:58] local.INFO: Identity Status:   
[2025-06-02 05:48:06] local.INFO: Identity Status:   
[2025-06-02 05:49:53] local.INFO: Identity Status:   
[2025-06-02 05:49:55] local.INFO: Identity Status:   
[2025-06-02 06:02:12] local.INFO: Identity Status:   
[2025-06-02 06:11:30] local.INFO: Identity Status:   
[2025-06-02 06:13:23] local.INFO: Identity Status:   
[2025-06-02 06:13:33] local.INFO: Identity Status:   
[2025-06-02 06:13:46] local.INFO: Identity Status:   
[2025-06-02 06:14:30] local.INFO: Identity Status:   
[2025-06-02 06:19:33] local.INFO: Identity Status:   
[2025-06-02 06:21:16] local.INFO: Identity Status:   
[2025-06-03 09:54:30] local.INFO: Identity Status:   
[2025-06-04 03:19:58] local.INFO: Identity Status:   
[2025-07-03 05:19:29] testing.INFO: bill_total  
[2025-07-03 05:29:07] testing.INFO: bill_total  
[2025-07-03 05:39:18] testing.INFO: bill_total  
[2025-07-03 05:59:53] testing.INFO: bill_total  
[2025-07-04 06:13:35] testing.INFO: bill_total  
[2025-07-04 06:21:01] testing.INFO: bill_total  
[2025-07-04 06:22:10] testing.INFO: bill_total  
[2025-07-04 06:28:51] testing.INFO: bill_total  
[2025-07-04 06:31:11] testing.INFO: bill_total  
[2025-07-04 06:33:26] testing.INFO: bill_total  
[2025-07-04 06:35:04] testing.INFO: bill_total  
[2025-07-04 06:36:47] testing.INFO: bill_total  
[2025-07-04 06:44:25] testing.INFO: bill_total  
[2025-07-04 07:57:31] testing.INFO: bill_total  
[2025-07-04 08:00:02] testing.INFO: bill_total  
[2025-07-04 08:13:11] testing.INFO: bill_total  
[2025-07-04 08:47:37] testing.INFO: bill_total  
[2025-07-04 08:49:10] testing.INFO: bill_total  
[2025-07-04 08:53:24] testing.INFO: bill_total  
[2025-07-04 09:02:15] testing.INFO: bill_total  
[2025-07-07 01:11:41] testing.INFO: bill_total  
[2025-07-07 01:21:04] testing.INFO: bill_total  
[2025-07-07 06:52:23] testing.INFO: bill_total  
[2025-07-08 01:28:16] testing.INFO: bill_total  
[2025-07-08 01:48:59] testing.INFO: bill_total  
[2025-07-08 01:59:41] testing.INFO: bill_total  
[2025-07-08 02:07:11] testing.INFO: bill_total  
[2025-07-08 02:07:46] testing.INFO: bill_total  
[2025-07-08 02:08:42] testing.INFO: bill_total  
[2025-07-08 02:11:32] testing.INFO: bill_total  
[2025-07-08 02:15:44] testing.INFO: bill_total  
[2025-07-08 04:33:15] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:222)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(138): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(125): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(699): Illuminate\\Log\\LogManager->driver()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(379): Illuminate\\Log\\LogManager->error('Could not const...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(342): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Google\\ApiCore\\ValidationException))
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Exceptions\\Handler.php(124): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(620): App\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Google\\ApiCore\\ValidationException))
#8 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-08 04:33:15] laravel.ERROR: Could not construct ApplicationDefaultCredentials {"exception":"[object] (Google\\ApiCore\\ValidationException(code: 0): Could not construct ApplicationDefaultCredentials at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php:337)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}

[previous exception] [object] (DomainException(code: 0): Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\auth\\src\\ApplicationDefaultCredentials.php:209)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(328): Google\\Auth\\ApplicationDefaultCredentials::getCredentials(NULL, Object(Google\\Auth\\HttpHandler\\Guzzle7HttpHandler), Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#15 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 {main}
"} 
[2025-07-08 04:34:57] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:222)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(138): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(125): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(699): Illuminate\\Log\\LogManager->driver()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(379): Illuminate\\Log\\LogManager->error('Could not const...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(342): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Google\\ApiCore\\ValidationException))
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Exceptions\\Handler.php(124): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(620): App\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Google\\ApiCore\\ValidationException))
#8 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-08 04:34:57] laravel.ERROR: Could not construct ApplicationDefaultCredentials {"exception":"[object] (Google\\ApiCore\\ValidationException(code: 0): Could not construct ApplicationDefaultCredentials at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php:337)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}

[previous exception] [object] (DomainException(code: 0): Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\auth\\src\\ApplicationDefaultCredentials.php:209)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(328): Google\\Auth\\ApplicationDefaultCredentials::getCredentials(NULL, Object(Google\\Auth\\HttpHandler\\Guzzle7HttpHandler), Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#15 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 {main}
"} 
[2025-07-08 04:36:14] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:222)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(138): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(125): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(699): Illuminate\\Log\\LogManager->driver()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(379): Illuminate\\Log\\LogManager->error('Could not const...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(342): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Google\\ApiCore\\ValidationException))
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Exceptions\\Handler.php(124): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(620): App\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Google\\ApiCore\\ValidationException))
#8 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-08 04:36:14] laravel.ERROR: Could not construct ApplicationDefaultCredentials {"exception":"[object] (Google\\ApiCore\\ValidationException(code: 0): Could not construct ApplicationDefaultCredentials at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php:337)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}

[previous exception] [object] (DomainException(code: 0): Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\auth\\src\\ApplicationDefaultCredentials.php:209)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(328): Google\\Auth\\ApplicationDefaultCredentials::getCredentials(NULL, Object(Google\\Auth\\HttpHandler\\Guzzle7HttpHandler), Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#15 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 {main}
"} 
[2025-07-08 04:37:37] laravel.EMERGENCY: Unable to create configured logger. Using emergency logger. {"exception":"[object] (InvalidArgumentException(code: 0): Log [] is not defined. at C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php:222)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(138): Illuminate\\Log\\LogManager->resolve(NULL, NULL)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(125): Illuminate\\Log\\LogManager->get(NULL)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Log\\LogManager.php(699): Illuminate\\Log\\LogManager->driver()
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(379): Illuminate\\Log\\LogManager->error('Could not const...', Array)
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions\\Handler.php(342): Illuminate\\Foundation\\Exceptions\\Handler->reportThrowable(Object(Google\\ApiCore\\ValidationException))
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Exceptions\\Handler.php(124): Illuminate\\Foundation\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(620): App\\Exceptions\\Handler->report(Object(Google\\ApiCore\\ValidationException))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(199): Illuminate\\Foundation\\Console\\Kernel->reportException(Object(Google\\ApiCore\\ValidationException))
#8 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 {main}
"} 
[2025-07-08 04:37:37] laravel.ERROR: Could not construct ApplicationDefaultCredentials {"exception":"[object] (Google\\ApiCore\\ValidationException(code: 0): Could not construct ApplicationDefaultCredentials at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php:337)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#4 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#14 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 {main}

[previous exception] [object] (DomainException(code: 0): Your default credentials were not found. To set up Application Default Credentials, see https://cloud.google.com/docs/authentication/external/set-up-adc at C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\auth\\src\\ApplicationDefaultCredentials.php:209)
[stacktrace]
#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(328): Google\\Auth\\ApplicationDefaultCredentials::getCredentials(NULL, Object(Google\\Auth\\HttpHandler\\Guzzle7HttpHandler), Array, NULL, NULL, Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\CredentialsWrapper.php(144): Google\\ApiCore\\CredentialsWrapper::buildApplicationDefaultCredentials(NULL, NULL, Array, NULL, NULL, Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\ClientOptionsTrait.php(303): Google\\ApiCore\\CredentialsWrapper::build(Array, 'googleapis.com')
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\gax\\src\\GapicClientTrait.php(308): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->createCredentialsWrapper(NULL, Array, 'googleapis.com')
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\google\\cloud-secret-manager\\src\\V1\\Gapic\\SecretManagerServiceGapicClient.php(526): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->setClientOptions(Array)
#5 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\RemoteSecretManager.php(26): Google\\Cloud\\SecretManager\\V1\\Gapic\\SecretManagerServiceGapicClient->__construct()
#6 C:\\1xampp\\htdocs\\sd-client\\app\\Modules\\SecretManager\\ManualSecretManagerImplementatation.php(13): App\\Modules\\SecretManager\\RemoteSecretManager->__construct()
#7 C:\\1xampp\\htdocs\\sd-client\\config\\app.php(7): App\\Modules\\SecretManager\\ManualSecretManagerImplementatation->__construct()
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): require('C:\\\\1xampp\\\\htdoc...')
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(96): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->Illuminate\\Foundation\\Bootstrap\\{closure}()
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(77): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFile(Object(Illuminate\\Config\\Repository), 'app', 'C:\\\\1xampp\\\\htdoc...', Array)
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\LoadConfiguration.php(38): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->loadConfigurationFiles(Object(Illuminate\\Foundation\\Application), Object(Illuminate\\Config\\Repository))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\LoadConfiguration->bootstrap(Object(Illuminate\\Foundation\\Application))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#15 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 {main}
"} 
[2025-07-08 02:39:06] testing.INFO: bill_total  
[2025-07-08 02:42:30] testing.INFO: bill_total  
[2025-07-08 03:38:40] testing.INFO: bill_total  
[2025-07-14 07:59:49] local.ERROR: Trait "App\Traits\UserLoggerTrait" not found  
[2025-07-14 08:00:16] local.ERROR: Trait "App\Traits\UserLoggerTrait" not found  
[2025-07-14 08:04:38] local.ERROR: Trait "App\Traits\UserLoggerTrait" not found  
[2025-07-17 05:36:55] local.ERROR: Declaration of App\Modules\DomainRedemption\Controllers\DomainRedemptionController::validate(App\Modules\DomainRedemption\Requests\RedemptionValidationRequest $request) must be compatible with App\Http\Controllers\Controller::validate(Illuminate\Http\Request $request, array $rules, array $messages = [], array $attributes = [])  
[2025-07-24 02:23:38] local.ERROR: Uncaught ErrorException: Undefined property: stdClass::$status in Command line code:1
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Undefined prope...', 'Command line co...', 1)
#1 Command line code(1): Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Undefined prope...', 'Command line co...', 1)
#2 {main}
  thrown  
[2025-08-06 02:28:05] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-06 02:29:03] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-06 02:30:09] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 01:40:06] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 01:40:09] local.ERROR: Refund failed for domain ID 112: Invoice not found or already refunded  
[2025-08-14 01:41:04] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 01:42:07] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 01:43:03] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 02:27:06] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 02:28:08] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 02:29:05] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 02:30:09] local.ERROR: Refund failed for domain ID 116: Invoice not found or already refunded  
[2025-08-14 05:07:06] local.ERROR: Refund failed for domain ID 121: Invoice not found or already refunded  
[2025-08-14 05:08:05] local.ERROR: Refund failed for domain ID 121: Invoice not found or already refunded  
[2025-08-14 05:09:04] local.ERROR: Refund failed for domain ID 121: Invoice not found or already refunded  
[2025-08-18 06:34:15] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Database\QueryException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Database\QueryException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Database\QueryException))
#4 {main}
  thrown  
[2025-08-19 05:23:07] local.ERROR: Refund failed for domain ID 2: Invoice not found or already refunded  
[2025-08-28 01:34:46] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:34:48] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:34:57] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:34:59] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:35:05] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:35:26] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:35:28] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-08-28 01:35:34] local.ERROR: Uncaught BadMethodCallException: Method Inertia\Response::send does not exist. in C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Macroable\Traits\Macroable.php:115
Stack trace:
#0 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Inertia\Response->__call('send', Array)
#1 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#2 C:\1xampp\htdocs\sd-client\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#3 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Routing\Exceptions\StreamedResponseException))
#4 {main}
  thrown  
[2025-09-01 01:17:07] local.ERROR: Refund failed for domain ID 120: Invoice not found or already refunded  
[2025-09-01 01:17:07] local.ERROR: Refund failed for domain ID 120: Invoice not found or already refunded  
