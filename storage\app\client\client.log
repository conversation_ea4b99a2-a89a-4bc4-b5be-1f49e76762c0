[2025-09-01 01:25:01] local.INFO: GeneralNotification: Running...  
[2025-09-01 01:25:01] local.INFO: GeneralNotification: done  
[2025-09-01 01:25:02] local.INFO: AfternicDomainTask: Running...  
[2025-09-01 01:25:02] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-09-01 01:25:03] local.INFO: AfternicManualTransfer: Running...  
[2025-09-01 01:25:03] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-09-01 01:26:01] local.INFO: JobRetryScheduler: Running...  
[2025-09-01 01:26:01] local.INFO: JobRetryScheduler: found 3, retrying jobs...  
[2025-09-01 01:26:01] local.ERROR: JobRetryScheduler: Undefined property: stdClass::$id  
[2025-09-01 01:26:01] local.ERROR: JobRetryScheduler: JobRetryScheduler: Undefined property: stdClass::$id  
[2025-09-01 01:26:01] local.ERROR: {"error":"Exception","message":"JobRetryScheduler: JobRetryScheduler: Undefined property: stdClass::$id","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\Client\\JobRetryScheduler->handle()
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\Client\\JobRetryScheduler), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 {main}"}  
[2025-09-01 01:26:01] local.ERROR: {"error":"Exception","message":"Scheduled command [\"C:\\1xampp\\php\\php.exe\" \"artisan\" client:job-retry-scheduler] failed with exit code [1].","url":"http:\/\/localhost\/strangedomains.local","code":0,"ip":"127.0.0.1","method":"GET","user_id":"guest","trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->Illuminate\\Console\\Scheduling\\{closure}()
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Factory.php(59): Illuminate\\Console\\View\\Components\\Task->render('<fg=gray>2025-0...', Object(Closure))
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(187): Illuminate\\Console\\View\\Components\\Factory->__call('task', Array)
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Scheduling\\ScheduleRunCommand.php(132): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->runEvent(Object(Illuminate\\Console\\Scheduling\\Event))
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Console\\Scheduling\\ScheduleRunCommand->handle(Object(Illuminate\\Console\\Scheduling\\Schedule), Object(Illuminate\\Events\\Dispatcher), Object(Illuminate\\Cache\\Repository), Object(NunoMaduro\\Collision\\Adapters\\Laravel\\ExceptionHandler))
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Console\\Scheduling\\ScheduleRunCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\1xampp\\htdocs\\sd-client\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 {main}"}  
[2025-09-01 01:26:02] local.INFO: GeneralNotification: Running...  
[2025-09-01 01:26:02] local.INFO: GeneralNotification: done  
[2025-09-01 01:26:03] local.INFO: AfternicDomainTask: Running...  
[2025-09-01 01:26:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-09-01 01:26:03] local.INFO: AfternicJobRetry: Running...  
[2025-09-01 01:26:03] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-09-01 01:26:04] local.INFO: AfternicManualTransfer: Running...  
[2025-09-01 01:26:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-09-01 01:26:05] local.INFO: AccountCreditor: Running...  
[2025-09-01 01:26:05] local.INFO: AccountCreditor: Done  
[2025-09-01 01:26:07] local.INFO: SessionPollChecker: Running...  
[2025-09-01 01:26:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-09-01 01:26:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-09-01 01:26:08] local.INFO: SessionPollChecker: Done  
[2025-09-01 01:27:01] local.INFO: ExpiredDomainTransferProcessor: Running...  
[2025-09-01 01:27:01] local.INFO: ExpiredDomainTransferProcessor: Checking for expired transfer requests...  
[2025-09-01 01:27:01] local.INFO: ExpiredDomainTransferProcessor: Terminating, nothing to process...  
[2025-09-01 01:27:02] local.INFO: GeneralNotification: Running...  
[2025-09-01 01:27:02] local.INFO: GeneralNotification: done  
[2025-09-01 01:27:03] local.INFO: AfternicDomainTask: Running...  
[2025-09-01 01:27:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-09-01 01:27:04] local.INFO: AfternicManualTransfer: Running...  
[2025-09-01 01:27:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-09-01 01:28:01] local.INFO: JobRetryScheduler: Running...  
[2025-09-01 01:28:01] local.INFO: JobRetryScheduler: Terminating, nothing to process..  
[2025-09-01 01:28:02] local.INFO: GeneralNotification: Running...  
[2025-09-01 01:28:02] local.INFO: GeneralNotification: done  
[2025-09-01 01:28:03] local.INFO: AfternicDomainTask: Running...  
[2025-09-01 01:28:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-09-01 01:28:04] local.INFO: AfternicJobRetry: Running...  
[2025-09-01 01:28:04] local.INFO: AfternicJobRetry: Terminating, nothing to process..  
[2025-09-01 01:28:04] local.INFO: AfternicManualTransfer: Running...  
[2025-09-01 01:28:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-09-01 01:28:05] local.INFO: AccountCreditor: Running...  
[2025-09-01 01:28:05] local.INFO: AccountCreditor: Done  
[2025-09-01 01:28:07] local.INFO: SessionPollChecker: Running...  
[2025-09-01 01:28:07] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-09-01 01:28:08] local.INFO: SessionPollChecker: Terminating, nothing to process..  
[2025-09-01 01:28:08] local.INFO: SessionPollChecker: Done  
[2025-09-01 01:29:02] local.INFO: GeneralNotification: Running...  
[2025-09-01 01:29:02] local.INFO: GeneralNotification: done  
[2025-09-01 01:29:03] local.INFO: AfternicDomainTask: Running...  
[2025-09-01 01:29:03] local.INFO: AfternicDomainTask: nothing to process... done  
[2025-09-01 01:29:03] local.INFO: AfternicManualTransfer: Running...  
[2025-09-01 01:29:04] local.INFO: AfternicManualTransfer: Terminating, nothing to process..  
[2025-09-01 01:29:09] local.INFO: DomainProcessCancellationHandler: Checking for domains...  
[2025-09-01 01:29:09] local.INFO: DomainProcessCancellationHandler: Skipping for 6 hours...  
[2025-09-01 01:29:20] local.INFO: DomainProcessCancellationHandler: Checking for domains...  
[2025-09-01 01:29:20] local.INFO: DomainProcessCancellationHandler: Skipping for 6 hours...  
[2025-09-01 03:01:10] local.INFO: DomainProcessCancellationHandler: Checking for domains...  
[2025-09-01 03:01:10] local.INFO: DomainProcessCancellationHandler: Skipping for 6 hours...  
[2025-09-01 07:28:02] local.INFO: <EMAIL> Updated domain id 111 to status PENDING  
ption","message":"","url":"http:\/\/www.mydomain.strangedomains.local\/push\/outgoing\/send","code":0,"ip":"127.0.0.1","method":"POST","user_id":7,"trace":"#0 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(50): Illuminate\\Foundation\\Application->abort(204, '', Array)
#1 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Precognition.php(17): abort(204, '', Array)
#2 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(449): Illuminate\\Foundation\\Precognition::Illuminate\\Foundation\\{closure}(Object(Illuminate\\Validation\\Validator))
#3 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(502): Illuminate\\Validation\\Validator->Illuminate\\Validation\\{closure}()
#4 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php(515): Illuminate\\Validation\\Validator->passes()
#5 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(31): Illuminate\\Validation\\Validator->fails()
#6 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#7 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1495): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\Push\\Requests\\OutgoingPushSendDomainRequest), Object(Illuminate\\Foundation\\Application))
#8 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1431): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\Push\\Requests\\OutgoingPushSendDomainRequest), Array)
#9 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1417): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Pus...', Object(App\\Modules\\Push\\Requests\\OutgoingPushSendDomainRequest))
#10 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Pus...', Object(App\\Modules\\Push\\Requests\\OutgoingPushSendDomainRequest))
#11 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Pus...', Array, true)
#12 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Pus...', Array)
#13 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Pus...', Array)
#14 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(92): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Pus...')
#15 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(51): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#16 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResolvesRouteDependencies.php(30): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#17 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(59): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\Push\\Controllers\\PushDomainController), 'send')
#18 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher.php(23): Illuminate\\Routing\\ControllerDispatcher->resolveParameters(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Push\\Controllers\\PushDomainController), 'send')
#19 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Foundation\\Routing\\PrecognitionControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\Push\\Controllers\\PushDomainController), 'send')
#20 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\HandlePrecognitiveRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\TwoFactorAuthenticationInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\AccountCreditInitialSetupMiddleware.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AccountCreditInitialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\PaymentMethodIntialSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\ContactSetupMiddleware.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\ContactSetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\IdentitySetupMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\IdentitySetupMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\CheckUserActiveStatus.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\CheckUserActiveStatus->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\1xampp\\htdocs\\sd-client\\app\\Http\\Middleware\\HandleHeaderLinks.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\HandleHeaderLinks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(32): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\1xampp\\htdocs\\sd-client\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Inertia\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#53 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#61 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#62 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#63 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#64 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#81 C:\\1xampp\\htdocs\\sd-client\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#82 C:\\1xampp\\htdocs\\sd-client\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#83 {main}"}  
[2025-09-01 07:28:05] local.INFO: <EMAIL> sent a push request for the domain LIMITTESTME.<NAME_EMAIL>.  
[2025-09-01 07:28:05] local.INFO: Domain History: Domain "limittestme.com" push request <NAME_EMAIL> to <EMAIL>.  
