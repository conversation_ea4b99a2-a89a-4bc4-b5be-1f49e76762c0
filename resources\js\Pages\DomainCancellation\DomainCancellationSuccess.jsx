import { Link, usePage } from "@inertiajs/react";
import PageFooter from "@/Components/Policy/Footer";
import PageHeader from "@/Components/Policy/Header";
import AccountCenterLayout from "@/Layouts/AccountCenterLayout";

export default function DomainCancellation() {
    const { userOn } = usePage().props;

    const content = (
        <div>
            <div className="mx-auto container max-w-[900px] text-center pt-14 flex justify-center items-center flex-wrap">
                <div className="flex w-auto h-[10rem]">
                    <img
                        className="h-full w-auto"
                        src="/assets/images/order_confirmed.svg"
                        alt="background"
                    />
                </div>
                <div className="flex flex-col items-start pl-4">
                        
                    <span className="text-l text-start text-gray-700 mt-2">
                        Thank you for your request. You will receive an email notification shortly regarding the status of your domain deletion request. Our support team will also be in touch via phone within 1–2 business days to confirm the request and assist with any further steps.
                    </span>
                </div>
            </div>

            <div className="w-full text-center flex flex-col items-center pt-12 space-y-4">
                <Link
                    href={route("domain")}
                    className="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none"
                >
                    Go to My Domain
                </Link>
            </div>
        </div>
    );

    return (
        <>
            {!userOn ? (
                <AccountCenterLayout
                    isReportAbusePage={true}
                    NewElementClassNamesIfHome="w-full"
                >
                    {content}
                </AccountCenterLayout>
            ) : (
                content
            )}
            <PageFooter />
        </>
    );
}
