<?php

use App\Console\Commands\Constants\SchedulerTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('cron_run_histories')->insert([
            'name' => SchedulerTypes::DOMAIN_PROCESS_CANCELLATION,
            'has_data' => false,
            'last_run_at' => Carbon::now()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('cron_run_histories')
            ->where('name', SchedulerTypes::DOMAIN_PROCESS_CANCELLATION)
            ->delete();
    }
};
