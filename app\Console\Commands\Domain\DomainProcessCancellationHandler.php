<?php

namespace App\Console\Commands\Domain;

use App\Console\Commands\Constants\SchedulerTypes;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\UpdateServices\DomainProcessCancellationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Exception;

class DomainProcessCancellationHandler extends Command
{
    private $limit = 500;
    private $maxDelay = 6;
    private $minDaysForCancellation = 44;
    private $maxDaysForCancellation = 45;

    protected $signature = 'domain:process-cancellation-handler';

    protected $description = 'Cancels all running processes/jobs for domains that are 44-45 days expired.';

    public function handle()
    {
        try {
            $this->evaluate();
        } catch (Exception $e) {
            $errorMsg = 'DomainProcessCancellationHandler: ' . $e->getMessage();
            app(AuthLogger::class)->error($errorMsg);
            throw new Exception($errorMsg);
        }
    }

    private function evaluate()
    {
        app(AuthLogger::class)->info('DomainProcessCancellationHandler: Checking for domains...');

        $skip = $this->validateCronHistory();

        if ($skip) return;

        $domains = $this->getDomainsForProcessCancellation();
        $hasData = $domains->isNotEmpty();

        if ($hasData) {
            app(AuthLogger::class)->info(message: 'DomainProcessCancellationHandler: Found ' . $domains->count() . ' domains for process cancellation...');
            DomainProcessCancellationService::instance()->cancelAllProcesses($domains);
        } else {
            app(AuthLogger::class)->info('DomainProcessCancellationHandler: No domains found for process cancellation...');
        }

        $this->updateCronHistory($hasData);
        app(AuthLogger::class)->info('DomainProcessCancellationHandler: Done');
    }

    private function getDomainsForProcessCancellation(): Collection
    {
        return DB::table('registered_domains')
            ->select(
                'domains.*',
                'registered_domains.id as registered_domain_id',
                'users.email as user_email',
                'users.id as user_id'
            )
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->where('registered_domains.status', '=', UserDomainStatus::OWNED)
            ->where('domains.status', '=', DomainStatus::EXPIRED)
            ->where('domains.expiry', '>=', Carbon::now()->subDays($this->maxDaysForCancellation)->getTimestampMs())
            ->where('domains.expiry', '<=', Carbon::now()->subDays($this->minDaysForCancellation)->getTimestampMs())
            ->limit($this->limit)->get();
    }

    private function getCron(): object
    {
        $cron = DB::table('cron_run_histories')->where('name', SchedulerTypes::DOMAIN_PROCESS_CANCELLATION)->first();

        if ($cron) return $cron;

        $errorMsg = 'DomainProcessCancellationHandler: No cron data found';
        app(AuthLogger::class)->error($errorMsg);
        throw new Exception($errorMsg);
    }

    private function validateCronHistory(): bool
    {
        $cron = $this->getCron();
        $lastRunAt = Carbon::parse($cron->last_run_at);
        $shouldRun = $cron->has_data || $lastRunAt->lessThanOrEqualTo(Carbon::now()->subHours($this->maxDelay));

        if ($shouldRun) return false;

        $errorMsg = 'DomainProcessCancellationHandler: Skipping for ' . $this->maxDelay . ' hours...';
        app(AuthLogger::class)->info($errorMsg);

        return true;
    }

    private function updateCronHistory(bool $hasData): void
    {
        DB::table('cron_run_histories')
            ->where('name', SchedulerTypes::DOMAIN_PROCESS_CANCELLATION)
            ->update(['has_data' => $hasData, 'last_run_at' => Carbon::now()]);
    }
}
