<?php

namespace App\Console;

use App\Console\Commands\AccountBalance\AccountCreditor;
use App\Console\Commands\Afternic\AfternicDomainTask;
use App\Console\Commands\Afternic\AfternicJobRetry;
use App\Console\Commands\Afternic\AfternicManualTransfer;
use App\Console\Commands\AuthenticatorApp\AuthenticatorAppDeleteUnathenticatedEntriesCommand;
use App\Console\Commands\Client\DomainExpiryEvaluator;
use App\Console\Commands\Client\JobRetryScheduler;
use App\Console\Commands\Domain\DomainAuthRegenerator;
use App\Console\Commands\Domain\DomainExportFileDeleteCommand;
use App\Console\Commands\Domain\DomainProcessCancellationHandler;
use App\Console\Commands\Domain\PostAutoRenewalGracePeriodHandler;
use App\Console\Commands\EmailOtp\EmailOtpDeleteUnathenticatedEntriesCommand;
use App\Console\Commands\Epp\SessionPollChecker;
use App\Console\Commands\Transfer\ExpiredDomainTransferProcessor;
use App\Console\Commands\Notification\GeneralNotification;
use App\Console\Commands\Domain\ProcessDomainDeletionRefunds;
use App\Console\Commands\Registration\RegistrationRequestExpirationDeleteCommand;
use App\Console\Commands\Registration\RegistrationVerificationCodeExpirationDeleteCommand;
use App\Console\Commands\Transaction\MonthlyTransactionsAggregator;
use App\Console\Commands\Transaction\SystemTransactionObserverHandler;
use App\Console\Commands\Transaction\WeeklyTransactionsAggregator;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        if (strcmp(config('app.env'), 'production') == 0) {
            $schedule->command(RegistrationRequestExpirationDeleteCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(RegistrationVerificationCodeExpirationDeleteCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(DomainExportFileDeleteCommand::class)->daily()->withoutOverlapping();
            $schedule->command(DomainExpiryEvaluator::class)->hourly()->withoutOverlapping();
            $schedule->command(JobRetryScheduler::class)->everyThreeMinutes()->withoutOverlapping();
            // $schedule->command(DomainAuthRegenerator::class)->daily()->withoutOverlapping();
            $schedule->command(ExpiredDomainTransferProcessor::class)->daily()->withoutOverlapping();
            $schedule->command(PostAutoRenewalGracePeriodHandler::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(DomainProcessCancellationHandler::class)->hourly()->withoutOverlapping();
            $schedule->command(WeeklyTransactionsAggregator::class)->weeklyOn(0)->withoutOverlapping();
            $schedule->command(MonthlyTransactionsAggregator::class)->monthlyOn(1)->withoutOverlapping();
            $schedule->command(SystemTransactionObserverHandler::class)->daily()->withoutOverlapping();
            $schedule->command(AfternicDomainTask::class)->everyTenMinutes()->withoutOverlapping();
            $schedule->command(AfternicJobRetry::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(GeneralNotification::class)->everyMinute()->withoutOverlapping();
            $schedule->command(AfternicManualTransfer::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(AccountCreditor::class)->everyFiveMinutes()->withoutOverlapping();
            $schedule->command(AuthenticatorAppDeleteUnathenticatedEntriesCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(EmailOtpDeleteUnathenticatedEntriesCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(ProcessDomainDeletionRefunds::class)->everyMinute()->withoutOverlapping();

            if (config('app.session_poll_checker')) {
                $schedule->command(SessionPollChecker::class)->everyFifteenMinutes()->withoutOverlapping();
            }
        } else {
            $schedule->command(RegistrationRequestExpirationDeleteCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(RegistrationVerificationCodeExpirationDeleteCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(DomainExportFileDeleteCommand::class)->daily()->withoutOverlapping();
            $schedule->command(DomainExpiryEvaluator::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(JobRetryScheduler::class)->everyTwoMinutes()->withoutOverlapping();
            // $schedule->command(DomainAuthRegenerator::class)->daily()->withoutOverlapping();
            $schedule->command(PostAutoRenewalGracePeriodHandler::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(DomainProcessCancellationHandler::class)->everyFifteenMinutes()->withoutOverlapping();
            $schedule->command(ExpiredDomainTransferProcessor::class)->everyThreeMinutes()->withoutOverlapping();
            $schedule->command(GeneralNotification::class)->everyMinute()->withoutOverlapping();
            $schedule->command(AfternicDomainTask::class)->everyMinute()->withoutOverlapping();
            $schedule->command(AfternicJobRetry::class)->everyTwoMinutes()->withoutOverlapping();
            $schedule->command(AfternicManualTransfer::class)->everyMinute()->withoutOverlapping();
            $schedule->command(AccountCreditor::class)->everyTwoMinutes()->withoutOverlapping();
            $schedule->command(AuthenticatorAppDeleteUnathenticatedEntriesCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(EmailOtpDeleteUnathenticatedEntriesCommand::class)->hourly()->withoutOverlapping();
            $schedule->command(ProcessDomainDeletionRefunds::class)->everyMinute()->withoutOverlapping();

            if (config('app.session_poll_checker')) {
                $schedule->command(SessionPollChecker::class)->everyTwoMinutes()->withoutOverlapping();
            }
        }
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
